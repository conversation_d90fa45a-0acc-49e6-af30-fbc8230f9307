/* 聊天页面样式 */

/* 整体容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 连接状态显示 */
.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.connection-status.connected {
  background-color: #e8f5e8;
  color: #4caf50;
}

.connection-status.disconnected {
  background-color: #ffeaa7;
  color: #e17055;
}

.status-text {
  margin-right: 10rpx;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background-color: #4caf50;
}

.status-indicator.offline {
  background-color: #e17055;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 商品信息卡片 */
.goods-card {
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-price {
  font-size: 28rpx;
  color: #ff8c00;
  font-weight: 500;
}

.goods-arrow {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 聊天消息列表 */
.message-list {
  flex: 1;
  overflow: hidden;
  padding: 0 20rpx;
}

.message-list-inner {
  padding-bottom: 30rpx;
}

.message-item {
  display: flex;
  width: calc(100% - 40rpx);
  margin-top: 30rpx;
  position: relative;
}

.avatar-container {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message-name {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.message-bubble {
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
  position: relative;
}

.message-bubble.image {
  padding: 10rpx;
  background-color: transparent !important;
}

.message-image {
  max-width: 400rpx;
  border-radius: 12rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  align-self: flex-start;
}

/* 对方的消息 - 靠左显示 */
.message-item.other {
  align-items: flex-start;
  flex-direction: row;
}

.message-item.other .message-content {
  margin-left: 20rpx;
  align-items: flex-start;
}

.message-item.other .message-bubble {
  background-color: #ffffff;
  color: #333;
  border-top-left-radius: 4rpx;
}

.message-item.other .message-time {
  align-self: flex-start;
}

/* 自己的消息 - 靠右显示 */
.message-item.self {
  align-items: flex-start;
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.message-item.self .message-content {
  margin-right: 20rpx;
  margin-left: 0;
  align-items: flex-end;
}

.message-item.self .message-bubble {
  background-color: #ff8c00;
  color: #ffffff;
  border-top-right-radius: 4rpx;
  border-top-left-radius: 12rpx;
}

.message-item.self .message-name {
  color: #666;
  text-align: right;
}

.message-item.self .message-time {
  align-self: flex-end;
  text-align: right;
}

/* 占位元素 */
.message-placeholder {
  height: 20rpx;
}

/* 底部输入区域 */
.input-area {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.input-actions {
  display: flex;
  margin-right: 20rpx;
}

.action-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.input-box {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
}

.message-input {
  height: 72rpx;
  font-size: 28rpx;
  width: 100%;
}

.send-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
  border-radius: 36rpx;
  background-color: #f5f5f5;
}

.send-btn.active {
  background-color: #ff8c00;
  color: #ffffff;
}
