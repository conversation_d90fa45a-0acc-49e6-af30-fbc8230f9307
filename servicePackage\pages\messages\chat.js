// 聊天页面
const dateUtil = require('../../../utils/dateUtil.js')
const app = getApp()
const messageApi = require('@/api/messageApi')
const commApi = require('@/api/commApi')
Page({
  data: {
    targetId: '', // 聊天对象ID
    targetName: '', // 聊天对象名称
    targetAvatar: '', // 聊天对象头像
    goodsId: '', // 商品ID（如果是商品相关的聊天）
    goodsInfo: null, // 商品信息
    inputContent: '', // 输入框内容
    messages: [], // 聊天消息列表
    scrollIntoView: '', // 滚动到指定消息
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/', // 图片访问路径
    userInfo: {},
    isConnected: false, // WebSocket连接状态
    connectionStatus: '未连接', // 连接状态文本
    pendingMessages: [] // 待发送的消息队列
  },

  onLoad: function (options) {
    // 获取参数
    const { targetId, targetName, goodsId, targetAvatar, goodsTitle, goodsImage, price } = options
    debugger
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: targetName || '聊天'
    })
    var usrInfo = wx.getStorageSync('userInfo')
debugger
    // 确保targetId不为空
    const finalTargetId = targetId;

    this.setData({
      userInfo: usrInfo,
      targetId: finalTargetId,
      targetName: targetName || '对方',
      targetAvatar: targetAvatar ? decodeURIComponent(targetAvatar) : '/images/default-avatar.svg',
      goodsId: goodsId || ''
    })

    console.log('聊天页面初始化，原始targetId:', targetId, '最终targetId:', finalTargetId, '类型:', typeof finalTargetId);

    // 如果有商品ID，加载商品信息
    if (goodsId) {
      // 如果传入了商品标题和图片，直接使用
      if (goodsTitle && goodsImage) {
        this.setData({
          goodsInfo: {
            id: goodsId,
            title: decodeURIComponent(goodsTitle),
            price: price,
            image: decodeURIComponent(goodsImage)
          }
        });
      } else {
        // 否则加载商品信息
        this.loadGoodsInfo(goodsId);
      }
    }

    // 获取WebSocket管理器
    this.websocketManager = app.getWebSocketManager();

    // 设置WebSocket消息监听
    this.setupWebSocketListeners();

        // 加载聊天记录
        this.loadChatHistory()

  },



  onShow: function () {
    console.log('chat页面onShow - 当前targetId:', this.data.targetId);

    // 更新连接状态
    this.updateConnectionStatus()
  },

  onHide: function () {
    // 页面隐藏时移除监听器
    if (this.websocketManager) {
      this.websocketManager.off('privateMessage', this.onPrivateMessage);
      this.websocketManager.off('messageResponse', this.onMessageResponse);
    }
  },

  // 设置WebSocket事件监听
  setupWebSocketListeners: function () {
    if (!this.websocketManager) {
      console.error('WebSocket管理器未找到');
      return;
    }

    // 绑定监听器到当前页面实例
    this.onPrivateMessage = this.handlePrivateMessage.bind(this);
    this.onMessageResponse = this.handleMessageResponse.bind(this);

    // 监听私聊消息
    this.websocketManager.on('privateMessage', this.onPrivateMessage);

    // 监听消息发送响应
    this.websocketManager.on('messageResponse', this.onMessageResponse);

    // 监听连接状态变化
    this.websocketManager.on('open', () => {
      this.updateConnectionStatus();
      this.processPendingMessages(); // 连接成功后处理待发送消息
    });

    this.websocketManager.on('close', () => {
      this.updateConnectionStatus();
    });

    this.websocketManager.on('error', () => {
      this.updateConnectionStatus();
    });
  },

  // 处理接收到的私聊消息
  handlePrivateMessage: function (reciveData) {
    console.log('收到私聊消息:', reciveData);
    const { userInfo, targetId } = this.data;

    console.log('当前targetId:', targetId, '(类型:', typeof targetId, ')');
    console.log('消息senderId:', reciveData.senderId, '(类型:', typeof reciveData.senderId, ')');
    console.log('当前用户ID:', userInfo.id, '(类型:', typeof userInfo.id, ')');
    console.log('消息receiverId:', reciveData.receiverId, '(类型:', typeof reciveData.receiverId, ')');

    // 检查是否是当前聊天对象发送的消息（使用字符串比较确保类型一致）
    const isTargetMatch = String(reciveData.senderId) === String(targetId);
    const isReceiverMatch = String(reciveData.receiverId) === String(userInfo.id);

    console.log('消息匹配检查 - targetMatch:', isTargetMatch, 'receiverMatch:', isReceiverMatch);

    if (isTargetMatch && isReceiverMatch) {
      const newMessage = {
        id: reciveData.id || 'msg_' + Date.now(),
        senderId: reciveData.senderId,
        receiverId: reciveData.receiverId,
        content: reciveData.content,
        type: reciveData.type,
        createTime: reciveData.createTime || dateUtil.formatTime(new Date()).substring(11, 16),
        status: reciveData.status,
        updateTime: reciveData.updateTime
      };

      // 添加到消息列表
      const messages = [...this.data.messages, newMessage];
      this.setData({ messages });

      // 滚动到最新消息
      this.scrollToBottom();
    }
  },

  // 处理消息发送响应
  handleMessageResponse: function (data) {
    console.log('消息发送响应:', data);

    if (data.success && data.responseId) {
      // 发送成功，可以在这里更新消息状态
      console.log('消息发送成功，消息ID:', data.responseId);
    } else {
      // 发送失败
      console.error('消息发送失败:', data);
      wx.showToast({
        title: '消息发送失败',
        icon: 'none'
      });
    }
  },

  // 更新连接状态
  updateConnectionStatus: function () {
    if (this.websocketManager) {
      const isConnected = this.websocketManager.isConnected();
      const readyState = this.websocketManager.getReadyState();

      let statusText = '未知';
      switch (readyState) {
        case 0: statusText = '连接中'; break;
        case 1: statusText = '已连接'; break;
        case 2: statusText = '关闭中'; break;
        case 3: statusText = '已关闭'; break;
      }

      this.setData({
        isConnected: isConnected,
        connectionStatus: statusText
      });
    }
  },

  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function (topic, data) {
    console.log('聊天页面收到WebSocket消息', { topic, data });

    switch (topic) {
      case 'privateMessage':
        // 处理接收到的私聊消息
        this.handlePrivateMessage(data);
        break;
      case 'messageResponse':
        // 处理消息发送响应
        this.handleSendResponse(data);
        break;
      default:
        console.log('聊天页面: 未知的WebSocket消息类型', topic);
    }
  },



  // 处理发送响应
  handleSendResponse: function (data) {
    if (data.success && data.responseId) {
      console.log('消息发送成功，消息ID:', data.responseId);
      // 可以在这里更新对应消息的状态
    } else {
      console.error('消息发送失败:', data);
      wx.showToast({
        title: '消息发送失败',
        icon: 'none'
      });
    }
  },

  // 加载商品信息
  loadGoodsInfo: function (goodsId) {
    // 模拟加载商品信息
    this.setData({
      goodsInfo: {
        id: goodsId,
        title: '全新iPhone 13 Pro Max 256GB 远峰蓝',
        price: '6999.00',
        image: 'https://img.freepik.com/free-photo/smartphone-balancing-with-blue-background_23-2150271746.jpg'
      }
    })
  },

  // 加载聊天记录
  loadChatHistory: function () {

    messageApi.getMessageRecord(this.data.targetId).then(messages => {
      console.log(messages)

      this.setData({
        messages
      })
      if (messages && messages.length > 0)
        // 滚动到最新消息
        this.scrollToBottom()
    }).catch(err => {
      console.error('getMessageRecord:', err);


    });


  },

  // 处理输入框内容变化
  handleInputChange: function (e) {
    this.setData({
      inputContent: e.detail.value
    })
  },

  // 发送消息
  sendMessage: function () {
    const { inputContent, targetId, isConnected } = this.data

    // 如果输入为空，不发送
    if (!inputContent.trim()) {
      return
    }

    const messageData = {
      type: 'text',
      content: inputContent,
      receiverId: targetId
    };

    // 检查连接状态
    if (!isConnected) {
      // 连接断开时，将消息加入待发送队列
      this.addToPendingMessages(messageData);
      wx.showToast({
        title: '连接中，消息将在连接后发送',
        icon: 'none'
      });

      // 清空输入框
      this.setData({ inputContent: '' });
      return;
    }

    // 发送消息
    this.doSendMessage(messageData);
  },

  // 实际发送消息的方法
  doSendMessage: function (messageData) {
    const { userInfo } = this.data;

    console.log('准备发送消息:', messageData);

    // 通过WebSocket发送消息
    const success = app.sendPrivateMessage(messageData.type, messageData.content, messageData.receiverId);

    if (success) {
      // 创建新消息并添加到列表
      const newMessage = {
        id: 'temp_' + Date.now(), // 临时ID，等服务器响应后可能会更新
        senderId: userInfo.id,
        receiverId: messageData.receiverId,
        content: messageData.content,
        type: messageData.type,
        createTime: dateUtil.formatTime(new Date()).substring(11, 16),
        status: 'sending' // 发送中状态
      }

      // 添加到消息列表
      const updatedMessages = [...this.data.messages, newMessage]
      this.setData({
        messages: updatedMessages,
        inputContent: '' // 清空输入框
      })

      // 滚动到最新消息
      this.scrollToBottom()
    } else {
      // 发送失败，加入待发送队列
      this.addToPendingMessages(messageData);
      wx.showToast({
        title: '发送失败，将在连接后重试',
        icon: 'none'
      });
    }
  },

  // 添加到待发送消息队列
  addToPendingMessages: function (messageData) {
    const pendingMessages = [...this.data.pendingMessages, messageData];
    this.setData({ pendingMessages });
  },

  // 处理待发送消息队列
  processPendingMessages: function () {
    const { pendingMessages } = this.data;

    if (pendingMessages.length > 0) {
      console.log('处理待发送消息队列，共', pendingMessages.length, '条消息');

      // 逐个发送待发送的消息
      pendingMessages.forEach(messageData => {
        this.doSendMessage(messageData);
      });

      // 清空待发送队列
      this.setData({ pendingMessages: [] });
    }
  },



  // 滚动到最新消息
  scrollToBottom: function () {
    const { messages } = this.data

    if (messages.length > 0) {
      this.setData({
        scrollIntoView: 'msg' + messages.length
      })
    }
  },

  // 点击商品卡片
  handleGoodsTap: function () {
    const { goodsId } = this.data

    if (goodsId) {
      wx.navigateTo({
        url: `/profilePackage/pages/goods/detail/detail?id=${goodsId}`
      })
    }
  },

  // 发送图片
  sendImage: function () {
    const { targetId } = this.data;

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 显示上传进度
        wx.showLoading({
          title: '上传中...'
        });

        // 上传图片
        commApi.upLoadFile(tempFilePath).then(uploadRes => {
          wx.hideLoading();

          if (uploadRes && uploadRes.data) {
            // 上传成功，发送图片消息
            const messageData = {
              type: 'image',
              content: uploadRes.data,
              receiverId: targetId
            };

            // 检查连接状态并发送
            const { isConnected } = this.data;
            if (!isConnected) {
              // 连接断开时，将消息加入待发送队列
              this.addToPendingMessages(messageData);
              wx.showToast({
                title: '连接中，图片将在连接后发送',
                icon: 'none'
              });
            } else {
              // 直接发送
              this.doSendMessage(messageData);
            }
          } else {
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          }
        }).catch(error => {
          wx.hideLoading();
          console.error('图片上传失败:', error);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage: function (e) {
    const { url } = e.currentTarget.dataset;

    // 确保URL是完整的
    const fullUrl = url.startsWith('http') ? url : this.data.apiUrl + url;

    wx.previewImage({
      current: fullUrl,
      urls: [fullUrl]
    });
  }
})
