// messages.js
const announcementApi = require('@/utils/announcement-api');
const commApi = require('@/api/commApi.js');
const noticeApi = require('@/api/notice.js');
const messageApi = require('@/api/messageApi.js');
const TabbarManager = require('../../utils/tabbar-manager.js');
const util = require('@/utils/util.js')

Page({
  data: {
    // 主要tab类型：system_message, notice_announcement, private_message
    mainTabs: [
      { key: 'system_message', name: '系统消息' },
      { key: 'notice_announcement', name: '通知公告' },
      { key: 'private_message', name: '站内私信' }
    ],
    currentMainTab: 'system_message',

    // 通知公告的子分类（只在notice_announcement tab下显示）
    noticeTypes: [],
    currentNoticeType: '',
    typesLoading: false,

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    total: 0,

    // 未读数量
    unreadCounts: {},
    // 消息数据
    messages: [],
    // 消息加载状态
    messagesLoading: false,
    showMessageModal: false,
    currentMessage: {},
    currentMessageIcon: 'icon-property',

    // 图片访问路径
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
  },

  onLoad: function () {
    // 初始化加载数据
    this.loadCurrentTabData();
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.refreshCurrentTab();

    // 设置底部 tabBar 选中状态
    TabbarManager.setTabbarSelected(this, 3);

    // 设置WebSocket消息监听
    this.setupWebSocketListeners();
  },

  onHide: function () {
    // 页面隐藏时移除WebSocket监听
    this.removeWebSocketListeners();
  },

  onUnload: function () {
    // 页面卸载时移除WebSocket监听
    this.removeWebSocketListeners();
  },

  // 设置WebSocket事件监听
  setupWebSocketListeners: function () {
    const app = getApp();
    this.websocketManager = app.globalData.websocketManager;

    if (!this.websocketManager) {
      console.log('Messages页面: WebSocket管理器未找到');
      return;
    }

    // 绑定监听器到当前页面实例
    this.onPrivateMessage = this.handlePrivateMessage.bind(this);

    // 监听私聊消息
    this.websocketManager.on('privateMessage', this.onPrivateMessage);

    console.log('Messages页面: WebSocket监听器已设置');
  },

  // 移除WebSocket事件监听
  removeWebSocketListeners: function () {
    if (this.websocketManager && this.onPrivateMessage) {
      this.websocketManager.off('privateMessage', this.onPrivateMessage);
      console.log('Messages页面: WebSocket监听器已移除');
    }
  },

  // 处理接收到的私聊消息
  handlePrivateMessage: function (message) {
    console.log('Messages页面收到私聊消息:', message);

    // 只有在私信tab时才处理
    if (this.data.currentMainTab === 'private_message') {
      // 刷新私信列表以显示新消息
      this.loadPrivateMessages();
    }
  },

  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function (topic, data) {
    console.log('Messages页面收到WebSocket消息', { topic, data });

    switch (topic) {
      case 'privateMessage':
        this.handlePrivateMessage(data);
        break;
      default:
        console.log('Messages页面: 未知的WebSocket消息类型', topic);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshCurrentTab();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.messagesLoading) {
      this.loadMoreData();
    }
  },

  // 主tab切换
  switchMainTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentMainTab) return;

    this.setData({
      currentMainTab: tab,
      pageNum: 1,
      messages: [],
      hasMore: true
    });

    this.loadCurrentTabData();
  },

  // 通知公告子分类切换
  switchNoticeType: function (e) {
    const type = e.currentTarget.dataset.type;
    if (type === this.data.currentNoticeType) return;

    this.setData({
      currentNoticeType: type,
      pageNum: 1,
      messages: [],
      hasMore: true
    });

    this.loadNoticeAnnouncementData();
  },

  // 加载当前tab的数据
  loadCurrentTabData: function () {
    const { currentMainTab } = this.data;

    switch (currentMainTab) {
      case 'system_message':
        this.loadSystemMessages();
        break;
      case 'notice_announcement':
        this.loadNoticeTypes();
        break;
      case 'private_message':
        this.loadPrivateMessages();
        break;
    }
  },

  // 刷新当前tab
  refreshCurrentTab: function () {
    this.setData({
      pageNum: 1,
      messages: [],
      hasMore: true
    });
    this.loadCurrentTabData();
  },

  // 加载更多数据
  loadMoreData: function () {
    if (!this.data.hasMore || this.data.messagesLoading) return;

    this.setData({
      pageNum: this.data.pageNum + 1
    });

    const { currentMainTab } = this.data;

    switch (currentMainTab) {
      case 'system_message':
        this.loadSystemMessages(true);
        break;
      case 'notice_announcement':
        this.loadNoticeAnnouncementData(true);
        break;
      case 'private_message':
        this.loadPrivateMessages(true);
        break;
    }
  },

  // 加载通知类型（仅在通知公告tab下使用）
  loadNoticeTypes: function () {
    this.setData({ typesLoading: true });

    commApi.getDictByNameEn('notice_type').then(res => {
      console.log('通知类型数据：', res);

      if (res && res.length > 0) {
        // 处理通知类型数据
        const noticeTypes = res.map(item => ({
          id: item.id,
          nameEn: item.nameEn,
          nameCn: item.nameCn,
          sort: item.sort || 0
        }));

        // 按排序字段排序
        noticeTypes.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        this.setData({
          noticeTypes: noticeTypes,
          currentNoticeType: noticeTypes[0]?.nameEn || '',
          typesLoading: false
        });

        // 加载第一个分类的数据
        if (noticeTypes.length > 0) {
          this.loadNoticeAnnouncementData();
        }
      } else {
        console.error('获取通知类型失败：', res);
        this.setData({ typesLoading: false });
      }
    }).catch(err => {
      console.error('获取通知类型异常：', err);
      this.setData({ typesLoading: false });
    });
  },

  // 加载系统消息
  loadSystemMessages: function (isLoadMore = false) {
    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    this.setData({ messagesLoading: true });

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize
    };

    messageApi.getSystemMessageList(params).then(res => {
      console.log('系统消息数据：', res);

      if (res && res.data) {
        const messageList = res.data.map(item => ({
          id: item.id,
          title: item.title || '无标题',
          content: item.content || '',
          time: this.formatMessageTime(item.createTime),
          type: 'system',
          imageUrl: item.imageUrl
        }));

        // 处理分页数据
        const messages = isLoadMore ? [...this.data.messages, ...messageList] : messageList;
        const hasMore = res.total > this.data.pageNum * this.data.pageSize;

        this.setData({
          messages: messages,
          total: res.total || 0,
          hasMore: hasMore,
          messagesLoading: false
        });
      } else {
        this.setData({ messagesLoading: false });
      }
    }).catch(err => {
      console.error('获取系统消息失败：', err);
      this.setData({ messagesLoading: false });
    });
  },

  // 加载通知公告数据
  loadNoticeAnnouncementData: function (isLoadMore = false) {
    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择社区');
      return;
    }

    this.setData({ messagesLoading: true });

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      type: this.data.currentNoticeType,
      communityId: selectedCommunity.id
    };

    noticeApi.getNoticePage(params).then(res => {
      console.log('通知公告数据：', res);

      if (res && res.list) {
        const messageList = res.list.map(item => ({
          id: item.id,
          title: item.title || '无标题',
          content: item.content || '',
          time: this.formatMessageTime(item.createTime),
          read: item.read !== undefined ? item.read : false,
          type: this.data.currentNoticeType
        }));

        // 处理分页数据
        const messages = isLoadMore ? [...this.data.messages, ...messageList] : messageList;
        const hasMore = res.total > this.data.pageNum * this.data.pageSize;

        this.setData({
          messages: messages,
          total: res.total || 0,
          hasMore: hasMore,
          messagesLoading: false
        });
      } else {
        this.setData({ messagesLoading: false });
      }
    }).catch(err => {
      console.error('获取通知公告失败：', err);
      this.setData({ messagesLoading: false });
    });
  },

  // 加载站内私信
  loadPrivateMessages: function (isLoadMore = false) {
    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    this.setData({ messagesLoading: true });

    messageApi.getPrivateMessageList().then(res => {
      console.log('站内私信数据：', res);

      if (res ) {
        const messageList = res.map(item => ({
          id: item.userId, // 使用userId作为唯一标识
          userId: item.userId,
          userName: item.userName || '未知用户',
          avatarUrl: item.avatarUrl,
          title: item.userName || '未知用户', // 私信列表显示用户名作为标题
          content: item.content || '',
          time: this.formatMessageTime(item.lastMessageTime),
          type: item.type || 'text',
          lastMessageTime: item.lastMessageTime
        }));

        // 私信列表不需要分页，直接显示全部
        this.setData({
          messages: messageList,
          total: messageList.length,
          hasMore: false,
          messagesLoading: false
        });
      } else {
        this.setData({ messagesLoading: false });
      }
    }).catch(err => {
      console.error('获取站内私信失败：', err);
      this.setData({ messagesLoading: false });
    });
  },

  // 处理私信列表项点击（进入聊天页面）
  handlePrivateMessageTap: function (e) {
    const message = e.currentTarget.dataset.message;

    // 跳转到聊天页面
    wx.navigateTo({
      url: `/servicePackage/pages/messages/chat?targetId=${message.userId}&targetName=${message.userName}&targetAvatar=${message.avatarUrl || ''}`
    });
  },

  // 格式化消息时间
  formatMessageTime: function (timeString) {
    if (!timeString) return '刚刚';

    try {
      const messageTime = new Date(timeString);
      const now = new Date();
      const diffDays = Math.floor((now - messageTime) / (24 * 60 * 60 * 1000));

      if (diffDays === 0) {
        return '今天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      } else if (diffDays === 1) {
        return '昨天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      } else {
        return (messageTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
          messageTime.getDate().toString().padStart(2, '0') + ' ' +
          messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      }
    } catch (error) {
      console.error('时间格式化失败：', error);
      return '刚刚';
    }
  },

  // 标签滚动提示点击
  onScrollHintTap: function () {
    // 触发标签容器滚动
    const query = wx.createSelectorQuery();
    query.select('.message-tabs-container').scrollTo({
      left: 200, // 向右滚动200rpx
      animated: true
    });
    query.exec();
  },

  // 加载公告数据（保留原有方法，用于兼容）
  loadAnnouncementData: function () {
    wx.showLoading({
      title: '加载中...'
    });

    // 调用API获取公告列表
    announcementApi.getAnnouncementList()
      .then(res => {
        const announcements = res.list || [];

        // 按类型分类公告
        const propertyNotices = [];
        const activityNotices = [];
        const emergencyNotices = [];

        // 处理公告数据
        announcements.forEach(item => {
          if (item.status !== 'published') return;

          // 格式化时间
          let timeText = '';
          if (typeof item.publishTime === 'string') {
            const publishTime = new Date(item.publishTime);
            const now = new Date();
            const diffDays = Math.floor((now - publishTime) / (24 * 60 * 60 * 1000));

            if (diffDays === 0) {
              timeText = '今天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            } else if (diffDays === 1) {
              timeText = '昨天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            } else {
              timeText = (publishTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
                publishTime.getDate().toString().padStart(2, '0') + ' ' +
                publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            }
          } else {
            timeText = '刚刚';
          }

          // 创建通知对象
          const notice = {
            id: item.id,
            title: item.title,
            time: timeText,
            read: false, // 默认未读
            content: item.content
          };

          // 根据类型分类
          switch (item.type) {
            case 'property_notice':
              propertyNotices.push(notice);
              break;
            case 'activity_notice':
              activityNotices.push(notice);
              break;
            case 'emergency_notice':
              emergencyNotices.push(notice);
              break;
          }
        });

        // 更新数据
        this.setData({
          'messages.property': propertyNotices,
          'messages.community': activityNotices,
          'messages.emergency': emergencyNotices,
          'unreadCounts.property': propertyNotices.filter(item => !item.read).length,
          'unreadCounts.community': activityNotices.filter(item => !item.read).length,
          'unreadCounts.emergency': emergencyNotices.filter(item => !item.read).length
        });

        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载公告数据失败', err);
        wx.hideLoading();

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 兼容旧的switchTab方法（现在用于通知公告的子分类切换）
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.switchNoticeType(e);
  },

  showMessageDetail: function (e) {
    const message = e.currentTarget.dataset.message;
    const { currentMainTab } = this.data;

    // 如果是站内私信，跳转到聊天页面
    if (currentMainTab === 'private_message') {
      this.handlePrivateMessageTap(e);
      return;
    }

    // 标记为已读（仅通知公告需要）
    if (currentMainTab === 'notice_announcement' && !message.read) {
      this.markMessageAsRead(message);
    }

    // 根据消息类型设置图标
    let icon = 'icon-property';
    if (message.type === 'system') {
      icon = 'icon-system';
    } else if (message.type === 'emergency_notice') {
      icon = 'icon-emergency';
    } else if (message.type === 'community_notice') {
      icon = 'icon-community';
    }

    // 生成消息内容
    let content = this.generateMessageContent(message);

    this.setData({
      showMessageModal: true,
      currentMessage: {
        ...message,
        content
      },
      currentMessageIcon: icon
    });
  },

  closeMessageModal: function () {
    this.setData({
      showMessageModal: false
    })

    noticeApi

  },

  stopPropagation: function (e) {
    // 阻止事件冒泡
  },

  markMessageAsRead: function (message) {
    // 更新消息已读状态（仅用于通知公告）
    const messages = [...this.data.messages];

    for (let i = 0; i < messages.length; i++) {
      if (messages[i].id === message.id) {
        messages[i].read = true;

        // 调用API设置已读状态
        noticeApi.setNoticeRead(messages[i].id).then(res => {
          console.log('设置已读成功');
        }).catch(err => {
          console.error('设置已读失败', err);
          // 如果API调用失败，恢复消息的未读状态
          messages[i].read = false;
          this.setData({ messages });
        });

        break;
      }
    }

    // 立即更新本地UI状态
    this.setData({ messages });
  },

  generateMessageContent: function (message) {
    // 如果消息有内容，直接使用
    if (message.content) {
      // 如果内容是HTML格式，去除HTML标签
      if (message.content.includes('<')) {
        return message.content.replace(/<[^>]+>/g, '');
      }
      return message.content;
    }


  },





})
